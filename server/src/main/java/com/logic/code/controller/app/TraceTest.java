package com.logic.code.controller.app;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/31 10:04
 * @desc
 */
public class TraceTest {

    public static void main1(String[] args) {
        List<Trace> list = new ArrayList<>();
        list.add(new Trace("快件在【武威凉州阳光城店】完成分拣，准备发往 【武威凉州转运站】", "2025-05-31 13:29"));
        list.add(new Trace("顺丰速运 已收取快件，您的期待，我们定竭诚守护，不负所托。如有疑问请电联快递员【黄德金，电话：15709355540】","2025-05-31 13:22"));
        //list.add(new Trace("",""));
        list = list.stream().sorted(Comparator.comparing(Trace::getDatetime).reversed()).collect(Collectors.toList());
        String jsonString = JSONObject.toJSONString(list);
        System.err.println(jsonString);
    }


    public static void main(String[] args) {
        String host = "https://kzexpress.market.alicloudapi.com";
        String path = "/api-mall/api/express/query";
        String method = "GET";
        String appcode = "80d6cd58b7874015906b5d17bbe470a5";
        Map<String, String> headers = new HashMap<String, String>();
        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
        headers.put("Authorization", "APPCODE " + appcode);
        Map<String, Object> querys = new HashMap<String, Object>();
        querys.put("expressNo", "SF1455068932940");
        querys.put("mobile", "13669260738");


        try {
            /**
             * 重要提示如下:
             * HttpUtils请从
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/src/main/java/com/aliyun/api/gateway/demo/util/HttpUtils.java
             * 下载
             *
             * 相应的依赖请参照
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/pom.xml
             */
            HttpResponse response = HttpRequest.get(host + path).header("Authorization", "APPCODE " + appcode).form(querys).execute();
            System.out.println(response.toString());
            System.err.println(response.body());
            //获取response的body
            //System.out.println(EntityUtils.toString(response.getEntity()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}

@Data
@AllArgsConstructor
class Trace {
    private String content;
    private String datetime;
}
