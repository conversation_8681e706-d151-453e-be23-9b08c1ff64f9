package com.logic.code.controller.test;

import com.logic.code.common.response.Result;
import com.logic.code.model.dto.WxShippingInfoDTO;
import com.logic.code.service.WxOrderShippingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信发货信息管理测试控制器
 * 仅在dev环境下可用
 */
@Slf4j
@RestController
@RequestMapping("/test/wx/shipping")
@Profile("dev")
public class WxShippingTestController {

    @Resource
    private WxOrderShippingService wxOrderShippingService;

    /**
     * 测试服务开通状态
     */
    @GetMapping("/test-service-status")
    public Result testServiceStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            Boolean isTradeManaged = wxOrderShippingService.isTradeManaged();
            result.put("isTradeManaged", isTradeManaged);
            result.put("message", isTradeManaged != null ?
                (isTradeManaged ? "服务已开通" : "服务未开通") : "查询失败");

            return Result.success(result);
        } catch (Exception e) {
            log.error("测试服务状态失败", e);
            result.put("error", e.getMessage());
            return Result.failure("测试失败");
        }
    }

    /**
     * 测试设置跳转路径
     */
    @PostMapping("/test-jump-path")
    public Result<Map<String, Object>> testSetJumpPath(@RequestParam(defaultValue = "pages/order/detail") String path) {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean success = wxOrderShippingService.setMsgJumpPath(path);
            result.put("success", success);
            result.put("path", path);
            result.put("message", success ? "设置成功" : "设置失败");

            return Result.success(result);
        } catch (Exception e) {
            log.error("测试设置跳转路径失败", e);
            result.put("error", e.getMessage());
            return Result.failure("测试失败");
        }
    }

    /**
     * 测试发货信息录入（模拟数据）
     */
    @PostMapping("/test-upload-shipping")
    public Result<Map<String, Object>> testUploadShipping() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 构建测试数据
            WxShippingInfoDTO dto = buildTestShippingInfo();

            boolean success = wxOrderShippingService.uploadShippingInfo(dto);
            result.put("success", success);
            result.put("testData", dto);
            result.put("message", success ? "上传成功" : "上传失败");

            return Result.success(result);
        } catch (Exception e) {
            log.error("测试发货信息录入失败", e);
            result.put("error", e.getMessage());
            return Result.failure("测试失败");
        }
    }

    /**
     * 测试查询订单状态
     */
    @GetMapping("/test-order-status")
    public Result<Map<String, Object>> testOrderStatus(@RequestParam(defaultValue = "test_transaction_id") String transactionId) {
        Map<String, Object> result = new HashMap<>();

        try {
            var orderInfo = wxOrderShippingService.getOrderInfo(transactionId, null, null);
            result.put("orderInfo", orderInfo);
            result.put("transactionId", transactionId);
            result.put("message", orderInfo != null ? "查询成功" : "查询失败");

            return Result.success(result);
        } catch (Exception e) {
            log.error("测试查询订单状态失败", e);
            result.put("error", e.getMessage());
            return Result.failure("测试失败");
        }
    }

    /**
     * 获取测试用的发货信息数据
     */
    @GetMapping("/get-test-data")
    public Result<WxShippingInfoDTO> getTestData() {
        try {
            WxShippingInfoDTO dto = buildTestShippingInfo();
            return Result.success(dto);
        } catch (Exception e) {
            log.error("获取测试数据失败", e);
            return Result.failure("获取测试数据失败: " + e.getMessage());
        }
    }

    /**
     * 构建测试用的发货信息
     */
    private WxShippingInfoDTO buildTestShippingInfo() {
        WxShippingInfoDTO dto = new WxShippingInfoDTO();

        // 订单信息
        WxShippingInfoDTO.OrderKey orderKey = new WxShippingInfoDTO.OrderKey();
        orderKey.setOrderNumberType(1); // 商户订单号
        orderKey.setMchid("test_mchid_123");
        orderKey.setOutTradeNo("test_order_" + System.currentTimeMillis());
        dto.setOrderKey(orderKey);

        // 物流信息
        dto.setLogisticsType(1); // 实体物流配送
        dto.setDeliveryMode(1);  // 统一发货

        // 物流详情
        WxShippingInfoDTO.ShippingItem shippingItem = new WxShippingInfoDTO.ShippingItem();
        shippingItem.setTrackingNo("TEST" + System.currentTimeMillis());
        shippingItem.setExpressCompany("YTO"); // 圆通快递
        shippingItem.setItemDesc("测试商品*1");

        // 联系方式
        WxShippingInfoDTO.Contact contact = new WxShippingInfoDTO.Contact();
        contact.setReceiverContact("138****1234");
        shippingItem.setContact(contact);

        dto.setShippingList(Arrays.asList(shippingItem));

        // 支付者信息
        WxShippingInfoDTO.Payer payer = new WxShippingInfoDTO.Payer();
        payer.setOpenid("test_openid_123456"); // 测试用openid
        dto.setPayer(payer);

        return dto;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "ok");
        result.put("timestamp", System.currentTimeMillis());
        result.put("message", "微信发货信息管理测试接口运行正常");
        return Result.success(result);
    }
}
