package com.logic.code.common.enmus;

import com.logic.code.common.response.ResultStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WeshopWechatResultStatus implements ResultStatus {

    SUCCESS("SUCCESS"),

    PARAMETER_VALIDATION_ERROR("PARAMETER_VALIDATION_ERROR"),

    GOODS_NOT_EXIST("GOODS_NOT_EXIST"),

    PHONE_NOT_EXIST("PHONE_NOT_EXIST"),

    CATEGORY_NOT_EXIST("CATEGORY_NOT_EXIST"),

    UPLOAD_FAIL("UPLOAD_FAIL"),

    FILE_NOT_EXIST("FILE_NOT_EXIST"),

    ORDER_NOT_EXIST("ORDER_NOT_EXIST"),

    PLEASE_SELECT_GOODS("PLEASE_SELECT_GOODS"),
    SYSTEM_ERROR("系统错误"),

    PLEASE_SELECT_SHIPPING_ADDRESS("PLEASE_SELECT_SHIPPING_ADDRESS"),

    CREATE_ORDER_ERROR("CREATE_ORDER_ERROR"),

    WESHOP_WECHAT_UNKNOWN_ERROR("WESHOP_WECHAT_UNKNOWN_ERROR"),

    GOODS_HAVE_BEEN_TAKEN_OFF_THE_SHELVES("610", "商品已下架"),
    UNDER_STOCK("611", "库存不足"),
    WECHAT_SERVICE_ERROR("615", "微信服务调用失败"),
    WECHAT_LOGIN_ERROR("616", "请先登陆"),
    TOKEN_EXPIRED("617", "登录已过期，请重新登录"),
    TOKEN_INVALID("618", "无效的登录凭证"),
    TOKEN_REFRESH_FAILED("619", "登录凭证刷新失败"),
    ORDER_CANCELED("620", "订单已取消"),
    ORDER_PAID("621", "订单已支付，请不要重复操作"),
    WECHAT_PAY_FAIL("622", "微信支付失败"),
    INSUFFICIENT_POINTS("623", "积分不足"),
    INSUFFICIENT_BALANCE("624", "余额不足"),
    POINTS_USE_FAILED("625", "积分使用失败"),
    BALANCE_USE_FAILED("626", "余额使用失败"),
    COUPON_USE_FAILED("627", "优惠券使用失败"),
    UNAUTHORIZED("7001", "无权限"),
    ;

    private String code;
    private String msg;

    WeshopWechatResultStatus(String status) {
        this.code = status;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
