package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logic.code.common.utils.MapEntityConverter;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.WxShippingInfo;
import com.logic.code.mapper.CommonMapper;
import logic.orm.WrapperBuilder;
import org.springframework.cache.annotation.Cacheable;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @param <T>
 * <AUTHOR>
 */
public abstract class BaseService<T> {


    protected abstract CommonMapper<T> getMapper();


    public List<T> queryAll() {
        return getMapper().selectList(null);
    }


    public List<T> queryList(Object entity) {
        return getMapper().selectList(WrapperBuilder.autoWhere(entity));
    }


    public T queryOne(T entity) {
        return getMapper().selectOne(WrapperBuilder.autoWhere(entity));
    }


    public T queryById(Serializable id) {
        return getMapper().selectById(id);
    }


    public List<T> queryByCriteria(Criteria<T, Object> criteria) {
        List<T> mapList = getMapper().selectBySql(criteria.buildSql());
        if (mapList == null || mapList.isEmpty()) {
            return new ArrayList<>();
        }

        return mapList;
    }
    public List<T> queryByCriteriaPage(Criteria<T, Object> criteria) {
        List<T> mapList = getMapper().selectBySql(criteria.buildSql());
        if (mapList == null || mapList.isEmpty()) {
            return new ArrayList<>();
        }

        return mapList;
    }

    public List<T> queryByCriteriaMap(Criteria<T, Object> criteria) {
        List<Map<String, Object>> mapList = getMapper().selectMapBySql(criteria.getTableName(), criteria.getExt());
        if (mapList == null || mapList.isEmpty()) {
            return new ArrayList<>();
        }

        // Get the entity class
        Class<T> entityClass = getEntityClass();

        // Use the utility to convert the list of maps to entities
        return MapEntityConverter.convertToEntityList(mapList, entityClass);
    }


    public T queryOneByCriteria(Criteria<T, Object> criteria) {
        List<T> ts = getMapper().selectBySql(criteria.buildSql());
        if (ts.size() == 0) {
            return null;
        }

        // Get the entity class
        Class<T> entityClass = getEntityClass();

        return ts.get(0);
        // Use the utility to convert the map to an entity
        //return MapEntityConverter.convertToEntity(ts.get(0), entityClass);
    }

    public int countByCriteria(Criteria<T, Object> criteria) {
        return getMapper().countBySql(criteria.buildCountSql());
    }


    public int create(T entity) {
        return getMapper().insert(entity);
    }


    public int createBatch(List<T> list) {
        for (T t : list) {
            getMapper().insert(t);
        }
        return list.size();
    }


    public int updateAll(T entity) {
        return getMapper().updateById(entity);
    }

    public int insert(T entity) {
        return getMapper().insert(entity);
    }

    /**
     * Insert multiple entities in batch
     */
    public int insertBatch(List<T> entities) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (T entity : entities) {
            count += getMapper().insert(entity);
        }
        return count;
    }


    public int updateNotNull(T entity) {
        return getMapper().updateById(entity);
    }
    public int updateById(T entity) {
        return getMapper().updateById(entity);
    }


    public int delete(T entity) {
        return getMapper().delete(WrapperBuilder.autoWhere(entity));
    }


    public int deleteById(Serializable id) {
        return getMapper().deleteById(id);
    }


    public int count(T entity) {
        return Math.toIntExact(getMapper().selectCount(WrapperBuilder.autoWhere(entity)));
    }
    public int count(QueryWrapper<T> wrapper) {
        return Math.toIntExact(getMapper().selectCount(wrapper));
    }

    /**
     * 获取实体类的Class对象
     * 使用反射从泛型参数中获取实体类型
     *
     * @return 实体类的Class对象
     */
    @Cacheable(value = "entityClass", key = "#root.target.class.name")
    protected Class<T> getEntityClass() {
        return (Class<T>) ((java.lang.reflect.ParameterizedType)
                this.getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    public Page<T> selectPage(Page<T> pageInfo, QueryWrapper<T> queryWrapper) {
        return getMapper().selectPage(pageInfo, queryWrapper);
    }

    public  List<Map<String, Object>> selectMaps(QueryWrapper<T> paidOrderWrapper){
        return getMapper().selectMaps(paidOrderWrapper);
    }

    public long count() {
        return getMapper().selectCount(null);
    }
}
