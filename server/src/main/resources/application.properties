spring.application.name=weshop-wjhx
server.port=9999
server.servlet.context-path=/weshop-wjhx

# HTTP????
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# ???????
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.url=***************************************************************************************************************************************************
#spring.datasource.username=logic
#spring.datasource.password=Tyhj@118
spring.datasource.url=************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Tyhj@118
#spring.datasource.url=****************************************************************************************************************************
#spring.datasource.username=root
#spring.datasource.password=Tyhj@118
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initial-size=1
spring.datasource.druid.max-active=20
spring.datasource.druid.min-idle=1
spring.datasource.druid.stat-view-servlet.allow=true
spring.datasource.druid.test-on-borrow=true
spring.datasource.druid.validation-query=SELECT 1
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# Remove any tk.mybatis.mapper.mapperProperties if it exists
# We will handle this in the configuration class instead
<!-- ???? -->
<el-col :span="24">
<el-form-item label="?????" prop="image">
<div class="main-image-container">
<div class="main-image-preview" v-if="formValidate.image">
<img :src="formatImageUrl(formValidate.image)" alt="????" />
<div class="image-actions">
<el-button size="mini" type="primary" @click="modalPicTap('main_image')" v-db-click>
<i class="el-icon-edit"></i> ??
</el-button>
<el-button size="mini" type="danger" @click="removeMainImage" v-db-click>
<i class="el-icon-delete"></i> ??
</el-button>
</div>
</div>
<div class="main-image-upload" v-else @click="modalPicTap('main_image')" v-db-click>
<i class="el-icon-picture-outline" style="font-size: 48px; color: #ddd;"></i>
<p style="margin-top: 10px; color: #999;">????????</p>
</div>
<div class="main-image-actions" v-if="formValidate.slider_image.length > 0">
<el-button size="mini" type="text" @click="useFirstSliderAsMain" v-db-click>
????????????
</el-button>
</div>
</div>
<div class="tips-info">?????800*800?????????????????</div>
<el-input v-model="formValidate.image" style="display: none"></el-input>
</el-form-item>
</el-col>

# General MyBatis configuration
mybatis.type-aliases-package=com.logic.code.entity
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.configuration.map-underscore-to-camel-case=true

# Configure tk.mybatis
mapper.mappers=tk.mybatis.mapper.common.BaseMapper,tk.mybatis.mapper.common.MySqlMapper
mapper.not-empty=false
mapper.identity=MYSQL
mapper.style=camelhump
mapper.safe-update=true
mapper.check-example-entity-class=true
# Base package for entity scanning
mapper.base-packages=com.logic.code.entity

# File upload settings
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
# Custom upload location (absolute path)
app.upload.dir=./uploads
app.upload.url=/weshop-wjhx/uploads

# Scan upload settings
app.scan.expiration=30
app.scan.url=http://localhost:9999/weshop-wjhx/scan

weshop.wx.miniapp.appid=wxca19c2771bc2ea41



weshop-wjhx.login-interceptor-exclude-path[0]=/
weshop-wjhx.login-interceptor-exclude-path[1]=/csrf
weshop-wjhx.login-interceptor-exclude-path[2]=/error
weshop-wjhx.login-interceptor-exclude-path[3]=/favicon.ico
weshop-wjhx.login-interceptor-exclude-path[4]=/swagger-resources/**
weshop-wjhx.login-interceptor-exclude-path[5]=/webjars/**
weshop-wjhx.login-interceptor-exclude-path[6]=/v2/**
weshop-wjhx.login-interceptor-exclude-path[7]=/swagger-ui.html/**
weshop-wjhx.login-interceptor-exclude-path[8]=/wechat/brand/**
weshop-wjhx.login-interceptor-exclude-path[9]=/wechat/catalog/**
weshop-wjhx.login-interceptor-exclude-path[10]=/wechat/goods/**
weshop-wjhx.login-interceptor-exclude-path[11]=/wechat/home/<USER>
weshop-wjhx.login-interceptor-exclude-path[12]=/wechat/search/**
weshop-wjhx.login-interceptor-exclude-path[13]=/wechat/topic/**
weshop-wjhx.login-interceptor-exclude-path[14]=/wechat/auth/login
weshop-wjhx.login-interceptor-exclude-path[15]=/wechat/dev/{userId}/token
weshop-wjhx.login-interceptor-exclude-path[16]=/uploads/**
weshop-wjhx.login-interceptor-exclude-path[17]=/api/**
weshop-wjhx.login-interceptor-exclude-path[18]=/wechat/index/**
weshop-wjhx.login-interceptor-exclude-path[19]=/redemption/**
weshop-wjhx.login-interceptor-exclude-path[20]=/wechat/pay/notify
weshop-wjhx.login-interceptor-exclude-path[21]=/wechat/pay/notifyRefund
weshop-wjhx.login-interceptor-exclude-path[22]=/adminapi/**
weshop-wjhx.login-interceptor-exclude-path[23]=/wechat/token/status
weshop-wjhx.login-interceptor-exclude-path[24]=/wechat/token/config
